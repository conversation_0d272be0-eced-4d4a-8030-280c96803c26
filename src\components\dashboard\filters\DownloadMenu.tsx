
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Download, FileText, FileSpreadsheet, File } from "lucide-react";
import { toast } from "@/hooks/use-toast";

export const DownloadMenu = () => {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async (format: string) => {
    setIsDownloading(true);
    
    // Simulate download API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    toast({
      title: "Download Complete",
      description: `Report downloaded as ${format.toUpperCase()}`,
    });
    
    setIsDownloading(false);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isDownloading}>
          <Download className="h-4 w-4 mr-2" />
          {isDownloading ? "Downloading..." : "Download Report"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48 bg-white">
        <DropdownMenuItem onClick={() => handleDownload('pdf')}>
          <FileText className="h-4 w-4 mr-2 text-red-500" />
          Download as PDF
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleDownload('excel')}>
          <FileSpreadsheet className="h-4 w-4 mr-2 text-green-500" />
          Download as Excel
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleDownload('word')}>
          <File className="h-4 w-4 mr-2 text-blue-500" />
          Download as Word
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
