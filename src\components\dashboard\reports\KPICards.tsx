
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Zap, Droplets, Leaf } from "lucide-react";
import { FilterState, RegionSelection } from "../ESGDashboard";

interface KPICardsProps {
  selectedRegion: RegionSelection;
  filters: FilterState;
}

interface KPIData {
  title: string;
  value: string;
  change: string;
  trend: "up" | "down";
  icon: React.ReactNode;
  color: string;
}

export const KPICards = ({ selectedRegion, filters }: KPICardsProps) => {
  // Mock data - in real app this would come from API
  const kpiData: KPIData[] = [
    {
      title: `Total Customer IT Load for ${filters.entity.length > 0 ? filters.entity[0] : 'All Entities'}`,
      value: "2,847 MWh",
      change: "+12.3%",
      trend: "up",
      icon: <Zap className="h-6 w-6" />,
      color: "text-blue-600"
    },
    {
      title: `Total Fuel Consumption for ${filters.entity.length > 0 ? filters.entity[0] : 'All Entities'}`,
      value: "1,456 liters",
      change: "-5.2%",
      trend: "down",
      icon: <Droplets className="h-6 w-6" />,
      color: "text-orange-600"
    },
    {
      title: `Renewable Energy Consumption % for ${filters.entity.length > 0 ? filters.entity[0] : 'All Entities'}`,
      value: "67.8%",
      change: "+8.1%",
      trend: "up",
      icon: <Leaf className="h-6 w-6" />,
      color: "text-green-600"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {kpiData.map((kpi, index) => (
        <Card key={index} className="relative overflow-hidden bg-white hover:shadow-lg transition-shadow">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div className={kpi.color}>
                {kpi.icon}
              </div>
              <div className={`flex items-center text-sm font-medium ${
                kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {kpi.trend === 'up' ? (
                  <TrendingUp className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-1" />
                )}
                {kpi.change}
              </div>
            </div>
            <CardTitle className="text-sm font-medium text-gray-600 line-clamp-2">
              {kpi.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{kpi.value}</div>
            <p className="text-xs text-gray-500 mt-1">
              Year: {filters.year.join(', ')} | Period: {filters.reportingPeriod}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
