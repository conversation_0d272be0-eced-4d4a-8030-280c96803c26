import { useState } from "react";
import { FilterSection } from "./filters/FilterSection";
import { HistoryModal } from "./HistoryModal";
import { FilterState, RegionSelection } from "./ESGDashboard";
import { format } from "date-fns";

interface ESGReportPanelProps {
  selectedRegion: RegionSelection;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
}

export const ESGReportPanel = ({ selectedRegion, filters, onFiltersChange }: ESGReportPanelProps) => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateReport = () => {
    setIsGenerating(true);
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false);
    }, 2000);
  };

  const getPeriodDisplay = () => {
    if (filters.reportingPeriod === "Custom" && filters.dateFrom && filters.dateTo) {
      return `${format(filters.dateFrom, "MMM dd")} - ${format(filters.dateTo, "MMM dd")}`;
    }
    return filters.year[0];
  };

  const getReportingPeriodRange = () => {
    switch (filters.reportingPeriod) {
      case "Q1":
        return "Jan - Mar";
      case "Q2":
        return "Apr - Jun";
      case "Q3":
        return "Jul - Sep";
      case "Q4":
        return "Oct - Dec";
      case "H1":
        return "Jan - Jun";
      case "H2":
        return "Jul - Dec";
      case "Annual":
        return "Jan - Dec";
      case "Custom":
        return filters.dateFrom && filters.dateTo 
          ? `${format(filters.dateFrom, "MMM dd")} - ${format(filters.dateTo, "MMM dd")}`
          : "";
      default:
        return "";
    }
  };

  const getMonthsForPeriod = () => {
    switch (filters.reportingPeriod) {
      case "Q1":
        return ["January", "February", "March"];
      case "Q2":
        return ["April", "May", "June"];
      case "Q3":
        return ["July", "August", "September"];
      case "Q4":
        return ["October", "November", "December"];
      case "H1":
        return ["January", "February", "March", "April", "May", "June"];
      case "H2":
        return ["July", "August", "September", "October", "November", "December"];
      case "Annual":
        return ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
      case "Custom":
        // For custom period, we'll show all months for now
        return ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
      default:
        return [];
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Fixed Header */}
      <div className="bg-white border-b border-gray-200 flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-bold text-gray-900 tracking-tight">ESG Report</h1>
                <p className="text-sm text-gray-500 mt-2 leading-relaxed">
                  {selectedRegion.region} • {selectedRegion.framework} • {selectedRegion.category}
                </p>
              </div>

              <div className="hidden md:flex flex-col items-end ml-6">
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900 leading-tight">
                    {new Date().toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </div>
                  <div className="text-sm text-gray-500 mt-1">Last Updated</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Fixed Filter Section */}
      <div className="bg-white border-b border-gray-200 shadow-sm flex-shrink-0 sticky top-0 z-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-4">
            <div className="mb-4">
              <h2 className="text-base font-semibold text-gray-900 leading-tight">Report Configuration</h2>
              <p className="text-sm text-gray-600 mt-1 leading-relaxed">Configure your filters and generate comprehensive ESG reports</p>
            </div>
            <FilterSection
              filters={filters}
              onFiltersChange={onFiltersChange}
              onGenerateReport={handleGenerateReport}
              onShowHistory={() => setIsHistoryOpen(true)}
              isGenerating={isGenerating}
            />
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-8">

          {/* IT Load Question Section */}
          {filters.entity[0] && (filters.year[0] || (filters.reportingPeriod === "Custom" && filters.dateFrom && filters.dateTo)) && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                  <div className="flex-1 min-w-0">
                    <h2 className="text-lg font-semibold text-gray-900 leading-tight mb-2">
                      Total Customer IT Load for {filters.entity[0]} for the {getPeriodDisplay()}
                    </h2>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      This metric shows the total IT load consumption for the selected entity and period
                    </p>
                  </div>
                  <div className="flex flex-col items-start lg:items-end min-w-[200px] bg-gray-50 rounded-lg p-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">Total Consumption</div>
                    <div className="text-2xl font-bold text-gray-900 mb-2">
                      {Math.floor(Math.random() * 5000 + 3000).toLocaleString()} MWh
                    </div>
                    <div className="text-sm font-medium text-gray-500">
                      Year-over-Year
                      <span className={`ml-2 text-sm font-semibold ${Math.random() > 0.5 ? 'text-green-600' : 'text-red-600'}`}>
                        {Math.random() > 0.5 ? '+' : '-'}{Math.floor(Math.random() * 20)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Fuel Consumption Question Section */}
          {filters.entity[0] && (filters.year[0] || (filters.reportingPeriod === "Custom" && filters.dateFrom && filters.dateTo)) && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                  <div className="flex-1 min-w-0">
                    <h2 className="text-lg font-semibold text-gray-900 leading-tight mb-2">
                      Total Fuel Consumption for {filters.entity[0]} for the {getPeriodDisplay()}
                    </h2>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      This metric shows the total fuel consumption for the selected entity and period
                    </p>
                  </div>
                  <div className="flex flex-col items-start lg:items-end min-w-[200px] bg-gray-50 rounded-lg p-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">Total Consumption</div>
                    <div className="text-2xl font-bold text-gray-900 mb-2">
                      {Math.floor(Math.random() * 5000 + 3000).toLocaleString()} L
                    </div>
                    <div className="text-sm font-medium text-gray-500">
                      Year-over-Year
                      <span className={`ml-2 text-sm font-semibold ${Math.random() > 0.5 ? 'text-green-600' : 'text-red-600'}`}>
                        {Math.random() > 0.5 ? '+' : '-'}{Math.floor(Math.random() * 20)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Water Consumption Question Section */}
          {filters.entity[0] && filters.reportingPeriod && (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 mb-8">
                  <div className="flex-1 min-w-0">
                    <h2 className="text-lg font-semibold text-gray-900 leading-tight mb-2">
                      Total Water Consumption for {filters.entity[0]} for the {filters.year[0]} ({getReportingPeriodRange()})
                    </h2>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      This metric shows the total water consumption for the selected entity and reporting period
                    </p>
                  </div>
                  <div className="flex flex-col items-start lg:items-end min-w-[200px] bg-gray-50 rounded-lg p-4">
                    <div className="text-sm font-medium text-gray-500 mb-1">Total Consumption</div>
                    <div className="text-2xl font-bold text-gray-900 mb-2">
                      {Math.floor(Math.random() * 10000 + 5000).toLocaleString()} m³
                    </div>
                    <div className="text-sm font-medium text-gray-500">
                      Year-over-Year
                      <span className={`ml-2 text-sm font-semibold ${Math.random() > 0.5 ? 'text-green-600' : 'text-red-600'}`}>
                        {Math.random() > 0.5 ? '+' : '-'}{Math.floor(Math.random() * 20)}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* Monthly Breakdown */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-base font-medium text-gray-900 mb-4">Monthly Breakdown</h3>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    {getMonthsForPeriod().map((month) => {
                      const consumption = Math.floor(Math.random() * 1000 + 500);
                      return (
                        <div key={month} className="bg-gray-50 rounded-lg p-4 text-center border border-gray-100 hover:border-gray-200 transition-colors">
                          <div className="text-sm font-medium text-gray-600 mb-2">{month.slice(0, 3)}</div>
                          <div className="text-lg font-semibold text-gray-900">{consumption.toLocaleString()}</div>
                          <div className="text-xs text-gray-500 mt-1">m³</div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* History Modal */}
      <HistoryModal
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
      />
    </div>
  );
};
