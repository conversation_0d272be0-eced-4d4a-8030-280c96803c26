import { useState } from "react";
import { FilterSection } from "./filters/FilterSection";
import { HistoryModal } from "./HistoryModal";
import { FilterState, RegionSelection } from "./ESGDashboard";
import { format } from "date-fns";

interface ESGReportPanelProps {
  selectedRegion: RegionSelection;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
}

export const ESGReportPanel = ({ selectedRegion, filters, onFiltersChange }: ESGReportPanelProps) => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateReport = () => {
    setIsGenerating(true);
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false);
    }, 2000);
  };

  const getPeriodDisplay = () => {
    if (filters.reportingPeriod === "Custom" && filters.dateFrom && filters.dateTo) {
      return `${format(filters.dateFrom, "MMM dd")} - ${format(filters.dateTo, "MMM dd")}`;
    }
    return filters.year[0];
  };

  const getReportingPeriodRange = () => {
    switch (filters.reportingPeriod) {
      case "Q1":
        return "Jan - Mar";
      case "Q2":
        return "Apr - Jun";
      case "Q3":
        return "Jul - Sep";
      case "Q4":
        return "Oct - Dec";
      case "H1":
        return "Jan - Jun";
      case "H2":
        return "Jul - Dec";
      case "Annual":
        return "Jan - Dec";
      case "Custom":
        return filters.dateFrom && filters.dateTo 
          ? `${format(filters.dateFrom, "MMM dd")} - ${format(filters.dateTo, "MMM dd")}`
          : "";
      default:
        return "";
    }
  };

  const getMonthsForPeriod = () => {
    switch (filters.reportingPeriod) {
      case "Q1":
        return ["January", "February", "March"];
      case "Q2":
        return ["April", "May", "June"];
      case "Q3":
        return ["July", "August", "September"];
      case "Q4":
        return ["October", "November", "December"];
      case "H1":
        return ["January", "February", "March", "April", "May", "June"];
      case "H2":
        return ["July", "August", "September", "October", "November", "December"];
      case "Annual":
        return ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
      case "Custom":
        // For custom period, we'll show all months for now
        return ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
      default:
        return [];
    }
  };

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">ESG Report </h1>
              <p className="text-sm text-gray-500 mt-1">
                {selectedRegion.region} • {selectedRegion.framework} • {selectedRegion.category}
              </p>
            </div>

            <div className="hidden md:block">
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">
                  {new Date().toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric',
                    year: 'numeric'
                  })}
                </div>
                <div className="text-sm text-gray-500">Last Updated</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Fixed Filter Section */}
      <div className="bg-white border-b border-gray-200 shadow-sm sticky top-[120px] z-20">
        <div className="px-6 py-3">
          <div className="mb-2">
            <h2 className="text-base font-semibold text-gray-900">Report Configuration</h2>
            <p className="text-xs text-gray-600">Configure your filters and generate comprehensive ESG reports</p>
          </div>
          <FilterSection
            filters={filters}
            onFiltersChange={onFiltersChange}
            onGenerateReport={handleGenerateReport}
            onShowHistory={() => setIsHistoryOpen(true)}
            isGenerating={isGenerating}
          />
        </div>
      </div>

      {/* IT Load Question Section */}
      {filters.entity[0] && (filters.year[0] || (filters.reportingPeriod === "Custom" && filters.dateFrom && filters.dateTo)) && (
        <div className="bg-white border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  Total Customer IT Load for {filters.entity[0]} for the {getPeriodDisplay()}
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  This metric shows the total IT load consumption for the selected entity and period
                </p>
              </div>
              <div className="flex flex-col items-end min-w-[180px]">
                <div className="text-sm font-medium text-gray-500">Total Consumption</div>
                <div className="text-xl font-bold text-gray-900">
                  {Math.floor(Math.random() * 5000 + 3000).toLocaleString()} MWh
                </div>
                <div className="text-sm font-medium text-gray-500 mt-1">Year-over-Year
                  <span className={`ml-2 text-sm font-semibold ${Math.random() > 0.5 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.random() > 0.5 ? '+' : '-'}{Math.floor(Math.random() * 20)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Fuel Consumption Question Section */}
      {filters.entity[0] && (filters.year[0] || (filters.reportingPeriod === "Custom" && filters.dateFrom && filters.dateTo)) && (
        <div className="bg-white border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  Total Fuel Consumption for {filters.entity[0]} for the {getPeriodDisplay()}
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  This metric shows the total fuel consumption for the selected entity and period
                </p>
              </div>
              <div className="flex flex-col items-end min-w-[180px]">
                <div className="text-sm font-medium text-gray-500">Total Consumption</div>
                <div className="text-xl font-bold text-gray-900">
                  {Math.floor(Math.random() * 5000 + 3000).toLocaleString()} L
                </div>
                <div className="text-sm font-medium text-gray-500 mt-1">Year-over-Year
                  <span className={`ml-2 text-sm font-semibold ${Math.random() > 0.5 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.random() > 0.5 ? '+' : '-'}{Math.floor(Math.random() * 20)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Water Consumption Question Section */}
      {filters.entity[0] && filters.reportingPeriod && (
        <div className="bg-white border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  Total Water Consumption for {filters.entity[0]} for the {filters.year[0]} ({getReportingPeriodRange()})
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  This metric shows the total water consumption for the selected entity and reporting period
                </p>
              </div>
              <div className="flex flex-col items-end min-w-[180px]">
                <div className="text-sm font-medium text-gray-500">Total Consumption</div>
                <div className="text-xl font-bold text-gray-900">
                  {Math.floor(Math.random() * 10000 + 5000).toLocaleString()} m³
                </div>
                <div className="text-sm font-medium text-gray-500 mt-1">Year-over-Year
                  <span className={`ml-2 text-sm font-semibold ${Math.random() > 0.5 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.random() > 0.5 ? '+' : '-'}{Math.floor(Math.random() * 20)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Monthly Breakdown */}
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Monthly Breakdown</h3>
              <div className="flex flex-wrap gap-4">
                {getMonthsForPeriod().map((month) => {
                  const consumption = Math.floor(Math.random() * 1000 + 500);
                  return (
                    <div key={month} className="flex flex-col items-center bg-gray-50 rounded-lg p-3 min-w-[120px]">
                      <span className="text-sm font-medium text-gray-600">{month}</span>
                      <span className="text-lg font-semibold text-gray-900 mt-1">{consumption.toLocaleString()} m³</span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* History Modal */}
      <HistoryModal
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
      />
    </div>
  );
};
