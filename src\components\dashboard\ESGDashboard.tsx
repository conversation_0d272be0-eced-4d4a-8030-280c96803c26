import { useState } from "react";
import { RegionAccordion } from "./RegionAccordion";
import { ESGReportPanel } from "./ESGReportPanel";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

export interface FilterState {
  year: string[];
  entity: string[];
  reportingPeriod: string;
  dateFrom: Date | null;
  dateTo: Date | null;
}

export interface RegionSelection {
  region: string;
  framework: string;
  category: string;
}

export const ESGDashboard = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<RegionSelection>({
    region: "Global",
    framework: "Internal Framework",
    category: "ESG"
  });

  const [filters, setFilters] = useState<FilterState>({
    year: [],
    entity: [],
    reportingPeriod: "",
    dateFrom: null,
    dateTo: null
  });

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <div className={`bg-white border-r border-gray-200 transition-all duration-300 ${
        isSidebarCollapsed ? 'w-12' : 'w-80'
      }`}>
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          {!isSidebarCollapsed && (
            <h2 className="text-lg font-semibold text-gray-800">ESG Navigator</h2>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
            className="ml-auto"
          >
            {isSidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>
        
        {!isSidebarCollapsed && (
          <RegionAccordion 
            selectedRegion={selectedRegion}
            onRegionSelect={setSelectedRegion}
          />
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <ESGReportPanel
          selectedRegion={selectedRegion}
          filters={filters}
          onFiltersChange={setFilters}
        />
      </div>
    </div>
  );
};
