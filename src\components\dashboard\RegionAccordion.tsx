
import { ChevronDown, ChevronRight } from "lucide-react";
import { useState } from "react";
import { RegionSelection } from "./ESGDashboard";

interface RegionAccordionProps {
  selectedRegion: RegionSelection;
  onRegionSelect: (selection: RegionSelection) => void;
}

interface RegionData {
  name: string;
  frameworks: {
    name: string;
    categories: string[];
  }[];
}

const regions: RegionData[] = [
  {
    name: "Global",
    frameworks: [
      {
        name: "Internal Framework",
        categories: ["ESG", "Customer"]
      },
      {
        name: "External Framework",
        categories: ["Audit"]
      }
    ]
  },
  {
    name: "India",
    frameworks: [
      {
        name: "Internal Framework",
        categories: ["ESG", "Customer"]
      },
      {
        name: "External Framework",
        categories: ["Audit"]
      }
    ]
  },
  {
    name: "Singapore",
    frameworks: [
      {
        name: "Internal Framework",
        categories: ["ESG", "Customer"]
      },
      {
        name: "External Framework",
        categories: ["Audit"]
      }
    ]
  },
  {
    name: "UK",
    frameworks: [
      {
        name: "Internal Framework",
        categories: ["ESG", "Customer"]
      },
      {
        name: "External Framework",
        categories: ["Audit"]
      }
    ]
  }
];

export const RegionAccordion = ({ selectedRegion, onRegionSelect }: RegionAccordionProps) => {
  const [expandedRegions, setExpandedRegions] = useState<string[]>(["Global"]);
  const [expandedFrameworks, setExpandedFrameworks] = useState<string[]>(["Global-Internal Framework"]);

  const toggleRegion = (regionName: string) => {
    setExpandedRegions(prev => 
      prev.includes(regionName) 
        ? prev.filter(r => r !== regionName)
        : [...prev, regionName]
    );
  };

  const toggleFramework = (regionName: string, frameworkName: string) => {
    const key = `${regionName}-${frameworkName}`;
    setExpandedFrameworks(prev => 
      prev.includes(key) 
        ? prev.filter(f => f !== key)
        : [...prev, key]
    );
  };

  const isSelected = (region: string, framework: string, category: string) => {
    return selectedRegion.region === region && 
           selectedRegion.framework === framework && 
           selectedRegion.category === category;
  };

  return (
    <div className="p-4 space-y-2">
      {regions.map((region) => (
        <div key={region.name} className="border border-gray-200 rounded-lg">
          <button
            onClick={() => toggleRegion(region.name)}
            className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 rounded-lg transition-colors"
          >
            <span className="font-medium text-gray-800">{region.name}</span>
            {expandedRegions.includes(region.name) ? (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedRegions.includes(region.name) && (
            <div className="border-t border-gray-200">
              {region.frameworks.map((framework) => (
                <div key={framework.name} className="ml-4">
                  <button
                    onClick={() => toggleFramework(region.name, framework.name)}
                    className="w-full px-4 py-2 flex items-center justify-between text-left hover:bg-gray-50 transition-colors"
                  >
                    <span className="text-sm text-gray-700">{framework.name}</span>
                    {expandedFrameworks.includes(`${region.name}-${framework.name}`) ? (
                      <ChevronDown className="h-3 w-3 text-gray-500" />
                    ) : (
                      <ChevronRight className="h-3 w-3 text-gray-500" />
                    )}
                  </button>
                  
                  {expandedFrameworks.includes(`${region.name}-${framework.name}`) && (
                    <div className="ml-4 pb-2">
                      {framework.categories.map((category) => (
                        <button
                          key={category}
                          onClick={() => onRegionSelect({
                            region: region.name,
                            framework: framework.name,
                            category
                          })}
                          className={`block w-full text-left px-4 py-2 text-sm rounded-md transition-colors ${
                            isSelected(region.name, framework.name, category)
                              ? 'bg-blue-100 text-blue-800 font-medium'
                              : 'text-gray-600 hover:bg-gray-50'
                          }`}
                        >
                          {category}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};
