
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, Eye, Calendar } from "lucide-react";
import { format } from "date-fns";

interface HistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface HistoryItem {
  id: string;
  timestamp: Date;
  region: string;
  framework: string;
  category: string;
  filters: {
    year: string[];
    entity: string[];
    reportingPeriod: string;
  };
  downloadCount: number;
}

export const HistoryModal = ({ isOpen, onClose }: HistoryModalProps) => {
  // Mock history data
  const [historyItems] = useState<HistoryItem[]>([
    {
      id: "1",
      timestamp: new Date(2024, 5, 1, 14, 30),
      region: "Global",
      framework: "Internal Framework",
      category: "ESG",
      filters: {
        year: ["2024"],
        entity: ["Entity A", "Entity B"],
        reportingPeriod: "Annual"
      },
      downloadCount: 3
    },
    {
      id: "2",
      timestamp: new Date(2024, 4, 28, 9, 15),
      region: "India",
      framework: "External Framework",
      category: "Audit",
      filters: {
        year: ["2023", "2024"],
        entity: ["Entity C"],
        reportingPeriod: "Q4"
      },
      downloadCount: 1
    },
    {
      id: "3",
      timestamp: new Date(2024, 4, 25, 16, 45),
      region: "UK",
      framework: "Internal Framework",
      category: "Customer",
      filters: {
        year: ["2024"],
        entity: [],
        reportingPeriod: "H1"
      },
      downloadCount: 5
    }
  ]);

  const handleView = (item: HistoryItem) => {
    console.log("View report:", item);
    // In real app, this would open the report in a new tab
  };

  const handleDownload = (item: HistoryItem, format: string) => {
    console.log("Download report:", item, format);
    // In real app, this would trigger the download
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] bg-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Report History</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 overflow-y-auto">
          {historyItems.map((item) => (
            <div key={item.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">
                      {format(item.timestamp, "MMM dd, yyyy 'at' HH:mm")}
                    </span>
                  </div>
                  <div className="font-medium text-gray-900">
                    {item.region} - {item.framework} ({item.category})
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => handleView(item)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleDownload(item, 'pdf')}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm text-gray-600">Filters Applied:</div>
                <div className="flex flex-wrap gap-2">
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-gray-500">Years:</span>
                    {item.filters.year.map((year) => (
                      <Badge key={year} variant="secondary" className="text-xs">
                        {year}
                      </Badge>
                    ))}
                  </div>
                  {item.filters.entity.length > 0 && (
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-gray-500">Entities:</span>
                      {item.filters.entity.map((entity) => (
                        <Badge key={entity} variant="secondary" className="text-xs">
                          {entity}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-gray-500">Period:</span>
                    <Badge variant="secondary" className="text-xs">
                      {item.filters.reportingPeriod}
                    </Badge>
                  </div>
                </div>
              </div>
              
              <div className="text-xs text-gray-500">
                Downloaded {item.downloadCount} time{item.downloadCount !== 1 ? 's' : ''}
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};
