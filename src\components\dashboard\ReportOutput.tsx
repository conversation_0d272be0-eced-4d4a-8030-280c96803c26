
import { KPICards } from "./reports/KPICards";
import { ChartsSection } from "./reports/ChartsSection";
import { FilterState, RegionSelection } from "./ESGDashboard";
import { Skeleton } from "@/components/ui/skeleton";
import { TrendingUp, <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON> } from "lucide-react";

interface ReportOutputProps {
  selectedRegion: RegionSelection;
  filters: FilterState;
  isGenerating: boolean;
}

export const ReportOutput = ({ selectedRegion, filters, isGenerating }: ReportOutputProps) => {
  if (isGenerating) {
    return (
      <div className="px-8 py-8">
        <div className="space-y-8">
          {/* Loading KPIs */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Skeleton className="h-6 w-6 rounded" />
              <Skeleton className="h-6 w-48" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-40 rounded-xl" />
              ))}
            </div>
          </div>
          
          {/* Loading Charts */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Skeleton className="h-6 w-6 rounded" />
              <Skeleton className="h-6 w-56" />
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-96 rounded-xl" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="px-8 py-8 space-y-10">
      {/* KPI Section */}
      <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
            <TrendingUp className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Key Performance Indicators</h2>
            <p className="text-gray-600">Real-time insights into your ESG performance metrics</p>
          </div>
        </div>
        <KPICards selectedRegion={selectedRegion} filters={filters} />
      </div>

      {/* Charts Section */}
      <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
            <BarChart3 className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Analytics & Insights</h2>
            <p className="text-gray-600">Comprehensive data visualization and trend analysis</p>
          </div>
        </div>
        <ChartsSection selectedRegion={selectedRegion} filters={filters} />
      </div>

      {/* Report Summary Footer */}
      <div className="mt-12 p-6 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Report Summary</h3>
            <p className="text-sm text-gray-600 mt-1">
              Generated for {filters.entity.length > 0 ? filters.entity.join(', ') : 'All Entities'} • 
              Year(s): {filters.year.join(', ')} • 
              Period: {filters.reportingPeriod}
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <PieChart className="h-4 w-4" />
            <span>Data updated in real-time</span>
          </div>
        </div>
      </div>
    </div>
  );
};
