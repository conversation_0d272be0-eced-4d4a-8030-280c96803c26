import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { FilterState, RegionSelection } from "../ESGDashboard";

interface ChartsSectionProps {
  selectedRegion: RegionSelection;
  filters: FilterState;
}

export const ChartsSection = ({ selectedRegion, filters }: ChartsSectionProps) => {
  // Mock data for charts
  const renewableEnergyData = [
    { name: 'Solar', y: 45, color: '#f59e0b' },
    { name: 'Wind', y: 30, color: '#3b82f6' },
    { name: 'Hydro', y: 15, color: '#06b6d4' },
    { name: 'Other', y: 10, color: '#10b981' }
  ];

  const waterWithdrawalByYear = [
    { name: '2020', y: 1200 },
    { name: '2021', y: 1150 },
    { name: '2022', y: 1100 },
    { name: '2023', y: 980 },
    { name: '2024', y: 920 }
  ];

  const waterWithdrawalByEntity = [
    { name: 'Entity A', y: 320 },
    { name: 'Entity B', y: 280 },
    { name: 'Entity C', y: 200 },
    { name: 'Entity D', y: 120 }
  ];

  const energyConsumptionData = [
    { name: 'Jan', renewable: 65, nonRenewable: 35 },
    { name: 'Feb', renewable: 70, nonRenewable: 30 },
    { name: 'Mar', renewable: 68, nonRenewable: 32 },
    { name: 'Apr', renewable: 72, nonRenewable: 28 },
    { name: 'May', renewable: 75, nonRenewable: 25 },
    { name: 'Jun', renewable: 78, nonRenewable: 22 }
  ];

  // Common chart options
  const commonOptions = {
    chart: {
      animation: {
        duration: 1000,
        easing: 'easeOutBounce'
      },
      style: {
        fontFamily: 'Inter, sans-serif'
      }
    },
    credits: {
      enabled: false
    },
    tooltip: {
      animation: true,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 0,
      borderRadius: 8,
      shadow: true,
      style: {
        fontSize: '12px'
      }
    }
  };

  // Renewable Energy Pie Chart options
  const pieChartOptions = {
    ...commonOptions,
    chart: {
      ...commonOptions.chart,
      type: 'pie'
    },
    title: {
      text: null
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '{point.name}: {point.percentage:.0f}%',
          style: {
            fontSize: '12px'
          }
        },
        animation: {
          duration: 1000
        }
      }
    },
    series: [{
      name: 'Energy Mix',
      data: renewableEnergyData,
      animation: {
        duration: 1000
      }
    }]
  };

  // Energy Consumption Bar Chart options
  const barChartOptions = {
    ...commonOptions,
    chart: {
      ...commonOptions.chart,
      type: 'column'
    },
    title: {
      text: null
    },
    xAxis: {
      categories: energyConsumptionData.map(d => d.name),
      crosshair: true
    },
    yAxis: {
      title: {
        text: 'Percentage'
      }
    },
    plotOptions: {
      column: {
        stacking: 'normal',
        animation: {
          duration: 1000
        }
      }
    },
    series: [{
      name: 'Renewable',
      data: energyConsumptionData.map(d => d.renewable),
      color: '#10b981'
    }, {
      name: 'Non-Renewable',
      data: energyConsumptionData.map(d => d.nonRenewable),
      color: '#ef4444'
    }]
  };

  // Water Withdrawal by Year Chart options
  const waterYearChartOptions = {
    ...commonOptions,
    chart: {
      ...commonOptions.chart,
      type: 'column'
    },
    title: {
      text: null
    },
    xAxis: {
      categories: waterWithdrawalByYear.map(d => d.name),
      crosshair: true
    },
    yAxis: {
      title: {
        text: 'Megalitres'
      }
    },
    plotOptions: {
      column: {
        animation: {
          duration: 1000
        }
      }
    },
    series: [{
      name: 'Water Withdrawn',
      data: waterWithdrawalByYear.map(d => d.y),
      color: '#3b82f6'
    }]
  };

  // Water Withdrawal by Entity Chart options
  const waterEntityChartOptions = {
    ...commonOptions,
    chart: {
      ...commonOptions.chart,
      type: 'bar'
    },
    title: {
      text: null
    },
    xAxis: {
      categories: waterWithdrawalByEntity.map(d => d.name),
      crosshair: true
    },
    yAxis: {
      title: {
        text: 'Megalitres'
      }
    },
    plotOptions: {
      bar: {
        animation: {
          duration: 1000
        }
      }
    },
    series: [{
      name: 'Water Withdrawn',
      data: waterWithdrawalByEntity.map(d => d.y),
      color: '#06b6d4'
    }]
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Renewable Energy Pie Chart */}
      <Card className="bg-white">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Renewable Energy Mix - {filters.entity.length > 0 ? filters.entity[0] : 'All Entities'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={pieChartOptions}
          />
        </CardContent>
      </Card>

      {/* Renewable Energy Bar Chart */}
      <Card className="bg-white">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Energy Consumption Trends - {filters.reportingPeriod}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={barChartOptions}
          />
        </CardContent>
      </Card>

      {/* Water Withdrawal by Year */}
      <Card className="bg-white">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Water Withdrawn by Year (Megalitres)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={waterYearChartOptions}
          />
        </CardContent>
      </Card>

      {/* Water Withdrawal by Entity */}
      <Card className="bg-white">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Water Withdrawn by Entity - {filters.year.join(', ')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <HighchartsReact
            highcharts={Highcharts}
            options={waterEntityChartOptions}
          />
        </CardContent>
      </Card>
    </div>
  );
};
