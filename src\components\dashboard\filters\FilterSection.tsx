import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { FilterState } from "../ESGDashboard";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { DownloadMenu } from "./DownloadMenu";

interface FilterSectionProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  onGenerateReport: () => void;
  onShowHistory: () => void;
  isGenerating: boolean;
}

const yearOptions = ["2024", "2023", "2022", "2021", "2020"];
const entityOptions = ["Entity A", "Entity B", "Entity C", "Entity D", "Entity E"];
const reportingPeriodOptions = ["Q1", "Q2", "Q3", "Q4", "H1", "H2", "Annual", "Custom"];

export const FilterSection = ({ 
  filters, 
  onFiltersChange, 
  onGenerateReport, 
  onShowHistory,
  isGenerating 
}: FilterSectionProps) => {
  const [showYearDropdown, setShowYearDropdown] = useState(false);
  const [showEntityDropdown, setShowEntityDropdown] = useState(false);

  const updateFilter = (key: keyof FilterState, value: FilterState[keyof FilterState]) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const isCustomPeriod = filters.reportingPeriod === "Custom";

  return (
    <div className="flex flex-col space-y-3 md:flex-row md:items-end md:space-y-0 md:space-x-4">
      {/* Year Filter */}
      <div className="flex flex-col flex-shrink-0 w-full md:w-auto">
        <label htmlFor="year-filter" className="text-sm font-medium text-gray-700">Year</label>
        <Select value={filters.year[0] || ""} onValueChange={(value) => updateFilter('year', [value])}>
          <SelectTrigger id="year-filter" className="h-9 text-sm">
            <SelectValue placeholder="Select year" />
          </SelectTrigger>
          <SelectContent>
              {yearOptions.map((year) => (
              <SelectItem key={year} value={year}>
                {year}
              </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>

      {/* Entity Filter */}
      <div className="flex flex-col flex-shrink-0 w-full md:w-auto">
        <label htmlFor="entity-filter" className="text-sm font-medium text-gray-700">Entity</label>
        <Select value={filters.entity[0] || ""} onValueChange={(value) => updateFilter('entity', [value])}>
          <SelectTrigger id="entity-filter" className="h-9 text-sm">
            <SelectValue placeholder="Select entity" />
          </SelectTrigger>
          <SelectContent>
              {entityOptions.map((entity) => (
              <SelectItem key={entity} value={entity}>
                {entity}
              </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>

      {/* Reporting Period */}
      <div className="flex flex-col flex-shrink-0 w-full md:w-auto">
        <label htmlFor="reporting-period-filter" className="text-sm font-medium text-gray-700">Reporting Period</label>
        <Select value={filters.reportingPeriod} onValueChange={(value) => updateFilter('reportingPeriod', value)}>
          <SelectTrigger id="reporting-period-filter" className="h-9 text-sm">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            {reportingPeriodOptions.map((period) => (
              <SelectItem key={period} value={period}>
                {period}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Date Range - Only shown when Custom is selected */}
      {isCustomPeriod && (
        <div className="flex flex-col flex-shrink-0 w-full md:w-auto">
          <label className="text-sm font-medium text-gray-700">Date Range</label>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex-1 h-9 text-sm">
                  {filters.dateFrom ? format(filters.dateFrom, "MMM dd") : "From"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dateFrom || undefined}
                  onSelect={(date) => updateFilter('dateFrom', date)}
                  initialFocus
                  className={cn("p-2 pointer-events-auto")}
                />
              </PopoverContent>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex-1 h-9 text-sm">
                  {filters.dateTo ? format(filters.dateTo, "MMM dd") : "To"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dateTo || undefined}
                  onSelect={(date) => updateFilter('dateTo', date)}
                  initialFocus
                  className={cn("p-2 pointer-events-auto")}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      )}

      {/* Action Buttons - Generate on Left, Others on Right */}
      <div className="flex flex-1 items-center justify-between gap-2 pt-1 md:pt-0">
        <Button
          onClick={onGenerateReport}
          disabled={isGenerating}
          size="sm"
          className="bg-blue-600 hover:bg-blue-700 h-9 text-sm font-medium"
        >
          {isGenerating ? "Generating..." : "Generate Report"}
        </Button>
        <div className="flex flex-wrap items-center gap-2">
          <DownloadMenu />
          <Button
            variant="outline"
            size="sm"
            onClick={onShowHistory}
            className="h-9 text-sm font-medium"
          >
            History
          </Button>
        </div>
      </div>
    </div>
  );
};
